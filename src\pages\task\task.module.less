.modal-operation {
  text-align: right;
  margin-top: 20px;
}

.add-task-moduel {
  :global(.ant-modal-body) {
    padding: 0 0 0 10px;
  }
}
.yth-inspection-moduel {
  position: relative;
  height: calc(100vh - 180px); // 弹窗高度：屏幕高度减去上下各50px留白
  overflow-y: auto; // 允许垂直滚动
  overflow-x: hidden; // 隐藏水平滚动
  padding-right: 10px; // 为滚动条留出空间
  box-sizing: border-box; // 确保padding包含在高度计算中
  margin-top: 10px;
}
.actual-end-time{
  margin: 10px 0;

}
/* 在task.module.less中 */
.custom-label {
  width: 40px !important;
  min-width: 40px !important;
}
.task-form{
  border-left: 1px solid #e4e4e4;
  border-right: 1px solid #e4e4e4;
  :global(.ant-form-item-label){
    font-size: 12px;
    background-color: #f2f2f2;
    >label{
      font-size: 12px;
      line-height: 30px !important;
      height: 30px !important;
    }
  }
  :global(.ant-form-item){
    margin-bottom: 0;
    line-height: 30px;
  }


  // 输入框控件字体大小
  :global(.ant-input) {
    font-size: 12px;
    line-height: 30px;
    border: 0;
  }

  // 选择器控件字体大小
  :global(.ant-select-selection-item) {
    font-size: 12px;
    line-height: 30px;
    border: 0;
  }

  // 日期选择器控件字体大小
   :global(.ant-picker) {
    border: 0 !important;
  }
  :global(.ant-picker-input > input) {
    font-size: 12px;
    line-height: 30px;
    border: 0;
  }

  // 文本域控件字体大小
  :global(.ant-input) {
    font-size: 12px;
    line-height: 30px;
  }

  // placeholder字体大小
  :global(.ant-input::placeholder) {
    font-size: 12px;
    line-height: 30px;
  }
  // 下拉框样式
  :global(.ant-select .ant-select-selector) {
    font-size: 12px;
    height: 30px !important;
    line-height: 30px;
    border: 0;
  }
  :global(.ant-select .ant-select-selection-search-input) {
    font-size: 12px;
    height: 30px !important;
    line-height: 30px;
  }
  :global(.ant-select .ant-select-selection-placeholder) {
    font-size: 12px;
    height: 30px !important;
    line-height: 30px !important;
  }
  // 下拉框选择后字体大小
  :global(.ant-select .ant-select-selection-item) {
    font-size: 12px;
    height: 30px !important;
    line-height: 30px;
  }
  // 下拉框选择后字体大小
  :global(.ant-select .actual-end-time > input) {
    font-size: 12px;
    height: 30px !important;
    line-height: 30px;
    border: 0;
  }
  :global(.ant-select-selection-placeholder) {
    font-size: 12px;
    line-height: 30px;
  }
  // 日期选择器placeholder字体大小
  :global(.ant-picker-input > input::placeholder) {
    font-size: 12px;
    line-height: 30px;
  }
  // 外层row边框
  :global( > .ant-row) {
    border-top: 1px solid #e4e4e4;
  }
  // form-label 高度
  .lable-form-item{
    :global(.ant-form-item-label){
      text-align: right;
      font-size: 12px;
      margin: 0;
      height: 32px;
      overflow: hidden;
      :global(>label){
        padding-right: 10px;
      }
    }
  }

  .center-label-form-item{
    :global(.ant-form-item-label){
      text-align: center;
      font-size: 12px;
      margin: 0;
      height: 32px;
      overflow: hidden;
      :global(>label){
        padding: 0;
      }
      :global(>label::after){
        margin: 0;
      }
    }
  }

}

